import { GoogleGenAI } from '@google/genai';
import { RateLimiter } from './rate-limiter';
import { generateFallbackSQL, cleanupQuery } from './fallback-sql-generator';
import { IntelligentSQLAgent } from './intelligent-sql-agent';
import { AgentFactory } from './agent-factory';
import {
  GEMINI_CONFIGS,
  parseGeminiError,
  optimizePrompt,
  validateGeminiResponse,
  calculateRetryDelay,
  RETRY_CONFIG
} from './gemini-config';

export class GoogleGeminiClient {
  private client: GoogleGenAI;
  private rateLimiter: RateLimiter;
  private static instance: GoogleGeminiClient;
  private intelligentAgent: IntelligentSQLAgent | null = null;

  private constructor() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY غير موجود في متغيرات البيئة');
    }

    this.client = new GoogleGenAI({
      apiKey: api<PERSON><PERSON>,
    });
    this.rateLimiter = RateLimiter.getInstance();
  }

  static getInstance(): GoogleGeminiClient {
    if (!GoogleGeminiClient.instance) {
      GoogleGeminiClient.instance = new GoogleGeminiClient();
    }
    return GoogleGeminiClient.instance;
  }

  // دالة مساعدة محسنة للتعامل مع أخطاء الشبكة وإعادة المحاولة
  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = RETRY_CONFIG.maxRetries,
    baseDelay: number = RETRY_CONFIG.baseDelay
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;

        // تحليل نوع الخطأ باستخدام الدالة المحسنة
        const errorInfo = parseGeminiError(error);

        // إعادة المحاولة فقط للأخطاء القابلة للإصلاح
        if (errorInfo.isRetryable && attempt < maxRetries) {
          const delay = calculateRetryDelay(attempt, errorInfo.type);
          console.log(`❌ المحاولة ${attempt} فشلت: ${errorInfo.message}. إعادة المحاولة خلال ${delay/1000} ثانية...`);

          // تطبيق إعدادات Rate Limiter المناسبة
          if (errorInfo.type === 'QUOTA_EXCEEDED') {
            this.rateLimiter.resetOnError();
          } else if (errorInfo.type === 'NETWORK_ERROR') {
            this.rateLimiter.resetOnNetworkError();
          }

          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }

        // إذا لم يكن خطأ قابل للإصلاح أو انتهت المحاولات
        console.error(`❌ فشل نهائي بعد ${attempt} محاولات: ${errorInfo.message}`);
        break;
      }
    }

    throw lastError;
  }

  // توليد وصف للجدول باستخدام Gemini مع إعادة المحاولة
  async generateTableDescription(
    tableName: string,
    columns: Array<{name: string, type: string, nullable: boolean}>,
    foreignKeys: Array<{columnName: string, referencedTable: string}>,
    sampleData?: unknown[]
  ): Promise<{
    description: string;
    analyticalValue: string;
    sqlExamples: Array<{
      query: string;
      explanation: string;
    }>;
    intelligentAnalysis: string;
    purpose: string;
    domain: string;
    businessContext: string;
    keyFields: string[];
    relatedTables: string[];
    limitations: string;
  }> {
    const prompt = this.buildTableAnalysisPrompt(tableName, columns, foreignKeys, sampleData);

    // استخدام دالة إعادة المحاولة المحسنة مع إعدادات محسنة
    const config = GEMINI_CONFIGS.TABLE_DESCRIPTION;
    const optimizedPrompt = optimizePrompt(`أنت محلل بيانات خبير ومتخصص في استخراج القيمة التحليلية من قواعد البيانات. مهمتك هي تحليل الجداول والتركيز على الاستخراجات والتحليلات المفيدة التي يمكن الحصول عليها من البيانات.

🎯 **هدفك الأساسي**: استخراج القيمة التحليلية والمعلومات المفيدة من الجدول، وليس مجرد وصف بنيته.

يجب أن تركز على:
1. 🧠 **التحليلات الممكنة**: ما هي الاستخراجات والتحليلات التي يمكن الحصول عليها؟
2. 🧮 **استعلامات SQL عملية**: نماذج حقيقية لاستعلامات مفيدة مع شرح كل منها
3. 📊 **التحليل الذكي**: اقتراحات تحليلية ذكية بناءً على البيانات المتاحة
4. 💡 **القيود والتنبيهات**: أي قيود أو اعتبارات مهمة
5. 🔗 **العلاقات التحليلية**: كيف يمكن ربط هذا الجدول مع جداول أخرى للحصول على تحليلات أعمق

${prompt}`, 'description');

    return await this.retryWithBackoff(async () => {
      await this.rateLimiter.waitForNextRequest();

      const response = await this.client.models.generateContent({
        model: config.model,
        contents: [
          {
            role: 'user',
            parts: [{ text: optimizedPrompt }]
          }
        ],
        config: {
          temperature: config.temperature,
          maxOutputTokens: config.maxOutputTokens,
          topP: config.topP,
          topK: config.topK
        }
      });

      const content = response.text;
      if (!content) {
        throw new Error('لم يتم الحصول على رد من Gemini');
      }

      // محاولة تحليل JSON مع التحقق من الصحة
      try {
        const parsed = JSON.parse(content);
        const expectedFields = ['description', 'analyticalValue', 'sqlExamples', 'intelligentAnalysis', 'purpose', 'domain', 'businessContext', 'keyFields', 'relatedTables', 'limitations'];
        const validation = validateGeminiResponse(parsed, expectedFields);

        if (validation.isValid) {
          return validation.data;
        } else {
          console.warn('⚠️ استجابة ناقصة من Gemini، الحقول المفقودة:', validation.missingFields);
          return parsed; // إرجاع البيانات حتى لو كانت ناقصة
        }
      } catch {
        // إذا فشل التحليل، نحاول استخراج JSON من النص
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
        throw new Error('فشل في تحليل رد Gemini كـ JSON');
      }
    });
  }

  // بناء prompt لتحليل الجدول مع التركيز على القيمة التحليلية
  private buildTableAnalysisPrompt(
    tableName: string,
    columns: Array<{name: string, type: string, nullable: boolean}>,
    foreignKeys: Array<{columnName: string, referencedTable: string}>,
    sampleData?: unknown[]
  ): string {
    let prompt = `🔍 أنت محلل بيانات خبير. مهمتك تحليل الجدول التالي واستخراج القيمة التحليلية منه:

📋 **اسم الجدول**: ${tableName}

📊 **الأعمدة**:
${columns.map(col =>
  `- ${col.name} (${col.type}) ${col.nullable ? '- يمكن أن يكون فارغ' : '- مطلوب'}`
).join('\n')}`;

    if (foreignKeys.length > 0) {
      prompt += `\n\n🔗 **المفاتيح الخارجية**:
${foreignKeys.map(fk =>
  `- ${fk.columnName} يشير إلى جدول ${fk.referencedTable}`
).join('\n')}`;
    }

    if (sampleData && sampleData.length > 0) {
      prompt += `\n\n📝 **عينة من البيانات**:
${JSON.stringify(sampleData.slice(0, 3), null, 2)}`;
    }

    prompt += `\n\n📌 **الإخراج المتوقع منك**:
لكل جدول تحلله، يجب أن تُخرج ما يلي:

1. 📘 **اسم الجدول**: ${tableName}
2. 🧠 **التحليلات الممكنة من هذا الجدول**:
   - مثال: حساب مبيعات منتج، تحليل أرباح فرع، مقارنة بين الموردين، تتبع أداء العملاء، إلخ.
3. 🧮 **نماذج لاستعلامات SQL** (مع شرح مختصر لكل استعلام):
   - \`SELECT column_name, SUM(amount) FROM ${tableName} GROUP BY column_name\`
     - شرح: يعرض إجمالي المبالغ حسب التصنيف.
4. 📝 **تحليل ذكي مقترح**:
   - مثال: "بناءً على هذا الجدول، يمكن تحليل أداء المنتجات حسب الفروع لاكتشاف المنتجات الأعلى مبيعًا في كل منطقة".
5. 💡 **تنبيهات خاصة أو قيود** (إن وجدت):
   - هل الجدول مؤقت؟ هل يعتمد على جدول آخر ضروري؟

📌 **ملاحظات مهمة**:
- لا تكرر وصف الجدول فقط؛ ركّز على القيمة التحليلية المستخرجة منه.
- لا تولد استعلامات بدون فهم العلاقات بين الجداول.
- إذا كان الجدول لا يحتوي على معلومات تحليلية مهمة، اذكر ذلك بوضوح.
- **أهم شيء: القيمة التحليلية - ماذا يمكن أن أستخرج من الجدول من معلومات مفيدة؟**

يرجى إرجاع النتيجة بصيغة JSON التالية:
{
  "description": "وصف مختصر للجدول ووظيفته الأساسية",
  "analyticalValue": "التحليلات والاستخراجات الممكنة من هذا الجدول",
  "sqlExamples": [
    {
      "query": "استعلام SQL مع أسماء الأعمدة الفعلية",
      "explanation": "شرح مختصر لما يحققه هذا الاستعلام"
    }
  ],
  "intelligentAnalysis": "تحليل ذكي مقترح بناءً على بنية الجدول والبيانات",
  "purpose": "الغرض الأساسي من الجدول",
  "domain": "المجال أو القطاع",
  "businessContext": "السياق التجاري أو الوظيفي للجدول",
  "keyFields": ["قائمة بأهم الحقول في الجدول"],
  "relatedTables": ["قائمة بالجداول المرتبطة المحتملة"],
  "limitations": "تنبيهات خاصة أو قيود (إن وجدت)"
}

تأكد من أن الإجابة بصيغة JSON صحيحة وباللغة العربية، مع التركيز على القيمة التحليلية والاستخراجات المفيدة.`;

    return prompt;
  }

  // توليد embeddings للنص باستخدام Gemini
  async generateEmbeddings(text: string): Promise<number[]> {
    try {
      // Gemini لا يدعم embeddings مباشرة، لذا سنستخدم text-embedding-004 من Google AI
      // أو يمكننا استخدام نموذج آخر للحصول على تمثيل رقمي للنص
      // في هذا المثال، سنقوم بتحويل النص إلى hash ثم إلى array من الأرقام
      // هذا حل مؤقت - في الواقع يجب استخدام embedding model مخصص
      
      const hash = this.simpleHash(text);
      const embedding = this.hashToEmbedding(hash, 1536); // نفس حجم OpenAI embeddings
      
      return embedding;
    } catch (error) {
      console.error('خطأ في توليد embeddings:', error);
      throw new Error(`فشل في توليد embeddings: ${error}`);
    }
  }

  // دالة مساعدة لتحويل النص إلى hash
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }

  // دالة مساعدة لتحويل hash إلى embedding vector
  private hashToEmbedding(hash: number, size: number): number[] {
    const embedding = new Array(size);
    let seed = hash;
    
    for (let i = 0; i < size; i++) {
      // استخدام Linear Congruential Generator لتوليد أرقام pseudo-random
      seed = (seed * 1664525 + 1013904223) % Math.pow(2, 32);
      embedding[i] = (seed / Math.pow(2, 32)) * 2 - 1; // تطبيع بين -1 و 1
    }
    
    // تطبيع الـ vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / magnitude);
  }

  // توليد استعلام SQL بناءً على سؤال المستخدم مع إعادة المحاولة
  async generateSQLQuery(
    userQuestion: string,
    relevantTables: Array<{
      name: string;
      description: string;
      columns: Array<{name: string, type: string}>;
      relationships: Array<{toTable: string, fromColumn: string, toColumn: string}>;
    }>,
    databaseType: 'mysql' | 'mssql'
  ): Promise<{
    query: string;
    explanation: string;
    confidence: number;
  }> {
    // محاولة استخدام الوكيل الذكي أولاً
    if (this.intelligentAgent) {
      try {
        console.log('🧠 استخدام الوكيل الذكي...');
        const result = await this.intelligentAgent.generateSQL(userQuestion);
        return {
          query: result.query,
          explanation: result.explanation,
          confidence: result.confidence
        };
      } catch (error) {
        console.error('❌ فشل الوكيل الذكي، التبديل للنظام التقليدي:', error);
      }
    }
    const prompt = this.buildSQLGenerationPrompt(userQuestion, relevantTables, databaseType);

    // استخدام دالة إعادة المحاولة المحسنة مع fallback
    try {
      const config = GEMINI_CONFIGS.SQL_GENERATION;
      const optimizedPrompt = optimizePrompt(`أنت خبير في كتابة استعلامات SQL متقدم وذكي. مهمتك تحليل سؤال المستخدم بعمق وتوليد استعلام SQL دقيق.

🧠 **منهجية التحليل الذكي:**
1. **فهم القصد:** حلل ما يريده المستخدم بالضبط
2. **تحديد الكيانات:** ما هي الجداول والأعمدة المطلوبة؟
3. **فهم العلاقات:** كيف ترتبط الجداول ببعضها؟
4. **تحديد المرشحات:** ما هي الشروط المطلوبة؟

⚠️ **قواعد أساسية:**
- للعملاء والمبيعات: customers → invoices → invoice_details → items
- stock_movements للمخزون الداخلي فقط (لا يحتوي customer_id)
- استخدم LIKE '%term%' للبحث النصي المرن
- استخدم أسماء الأعمدة الصحيحة من الجداول المتاحة

🎯 **أمثلة ذكية:**
- "مبيعات البرتقال" → WHERE item_name LIKE '%برتقال%'
- "أكثر العملاء" → GROUP BY customer + ORDER BY SUM DESC
- "مشتريات فاطمة" → WHERE customer_name LIKE '%فاطمة%'

📊 **تحليل السياق:**
- إذا ذُكر منتج محدد → أضف شرط على item_name
- إذا ذُكر عميل محدد → أضف شرط على customer_name
- إذا ذُكر تاريخ → أضف شرط على التاريخ المناسب

${prompt}`, 'sql');

      return await this.retryWithBackoff(async () => {
        await this.rateLimiter.waitForNextRequest();

        const response = await this.client.models.generateContent({
          model: config.model,
          contents: [
            {
              role: 'user',
              parts: [{ text: optimizedPrompt }]
            }
          ],
          config: {
            temperature: config.temperature,
            maxOutputTokens: config.maxOutputTokens,
            topP: config.topP,
            topK: config.topK
          }
        });

        const content = response.text;
        if (!content) {
          throw new Error('لم يتم الحصول على رد من Gemini');
        }

        // تحليل JSON مع التحقق من الصحة
        try {
          const parsed = JSON.parse(content);
          const expectedFields = ['query', 'explanation', 'confidence'];
          const validation = validateGeminiResponse(parsed, expectedFields);

          if (validation.isValid) {
            return validation.data;
          } else {
            console.warn('⚠️ استجابة SQL ناقصة، الحقول المفقودة:', validation.missingFields);
            return parsed; // إرجاع البيانات حتى لو كانت ناقصة
          }
        } catch {
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            return JSON.parse(jsonMatch[0]);
          }
          throw new Error('فشل في تحليل رد Gemini كـ JSON');
        }
      });

    } catch (error: any) {
      // إذا فشلت جميع المحاولات، استخدم نظام fallback
      console.log('فشل في توليد SQL عبر API، محاولة استخدام نظام fallback...');

      const fallbackResult = generateFallbackSQL(userQuestion, relevantTables);
      if (fallbackResult) {
        return {
          query: cleanupQuery(fallbackResult.query),
          explanation: `${fallbackResult.explanation}\n\n⚠️ تم استخدام نظام fallback بسبب مشكلة في API.`,
          confidence: fallbackResult.confidence
        };
      }

      throw new Error(`فشل في توليد استعلام SQL: ${error?.message || error}`);
    }
  }

  // بناء prompt لتوليد SQL
  private buildSQLGenerationPrompt(
    userQuestion: string,
    relevantTables: Array<{
      name: string;
      description: string;
      columns: Array<{name: string, type: string}>;
      relationships: Array<{toTable: string, fromColumn: string, toColumn: string}>;
    }>,
    databaseType: 'mysql' | 'mssql'
  ): string {
    const prompt = `🎯 **مهمة تحليل ذكي لسؤال SQL:**

**السؤال:** "${userQuestion}"
**نوع قاعدة البيانات:** ${databaseType}

📋 **الجداول والبيانات المتاحة:**
${relevantTables.map(table => `
**جدول: ${table.name}**
- الوصف: ${table.description}
- الأعمدة: ${table.columns.map(col => `${col.name} (${col.type})`).join(', ')}
- العلاقات: ${table.relationships.map(rel => `${rel.fromColumn} → ${rel.toTable}.${rel.toColumn}`).join(', ')}
`).join('\n')}

🧠 **تحليل السؤال:**
1. حلل السؤال لفهم القصد الحقيقي
2. حدد الكيانات المطلوبة (عملاء، منتجات، مبيعات، إلخ)
3. اختر الجداول المناسبة بناءً على الوصف والعلاقات
4. استخدم البحث المرن (LIKE) للنصوص
5. طبق المرشحات المناسبة

⚡ **قواعد ذكية مهمة:**
- للعملاء والمبيعات: customers → invoices → invoice_details → items
- للبحث عن منتج: WHERE item_name LIKE '%اسم_المنتج%'
- للبحث عن عميل: WHERE customer_name LIKE '%اسم_العميل%'
- stock_movements للمخزون الداخلي فقط
- ⚠️ **مهم جداً**: استخدم جدول 'items' وليس 'products' للمبيعات والمقارنات
- جدول 'products' لا يرتبط بـ 'invoice_details' ولا يحتوي على بيانات المبيعات الفعلية
- للمقارنات: استخدم items مع invoice_details دائماً

📤 **المطلوب - JSON:**
{
  "query": "استعلام SQL محسن وذكي",
  "explanation": "شرح واضح لكيفية حل السؤال",
  "confidence": 0.9
}`;

    return prompt;
  }

  // إنشاء وكيل ذكي من قاعدة البيانات
  async initializeIntelligentAgent(
    relevantTables: Array<{
      name: string;
      description: string;
      columns: Array<{name: string, type: string}>;
      relationships: Array<{toTable: string, fromColumn: string, toColumn: string}>;
    }>
  ): Promise<void> {
    try {
      console.log('🚀 إنشاء وكيل ذكي...');

      // تحويل الجداول إلى schema للوكيل الذكي
      const schema = {
        tables: relevantTables.map(table => ({
          name: table.name,
          description: table.description,
          columns: table.columns.map(col => ({
            name: col.name,
            type: col.type,
            description: `عمود ${col.name} من نوع ${col.type}`,
            nullable: false,
            isPrimaryKey: col.name.includes('_id') && col.name.endsWith('_id'),
            isForeignKey: col.name.includes('_id') && !col.name.endsWith(table.name.slice(0, -1) + '_id')
          })),
          relationships: table.relationships.map(rel => ({
            fromTable: table.name,
            fromColumn: rel.fromColumn,
            toTable: rel.toTable,
            toColumn: rel.toColumn,
            type: 'one-to-many' as const
          }))
        }))
      };

      this.intelligentAgent = AgentFactory.createFromSchema(schema, this);
      console.log('✅ تم إنشاء الوكيل الذكي بنجاح');

    } catch (error) {
      console.error('❌ فشل في إنشاء الوكيل الذكي:', error);
      this.intelligentAgent = null;
    }
  }

  // توليد محتوى باستخدام Gemini (للوكيل الذكي)
  async generateContent(prompt: string): Promise<string> {
    const config = GEMINI_CONFIGS.GENERAL_CONTENT;
    const optimizedPrompt = optimizePrompt(prompt, 'analysis');

    return await this.retryWithBackoff(async () => {
      await this.rateLimiter.waitForNextRequest();

      const response = await this.client.models.generateContent({
        model: config.model,
        contents: [
          {
            role: 'user',
            parts: [{ text: optimizedPrompt }]
          }
        ],
        config: {
          temperature: config.temperature,
          maxOutputTokens: config.maxOutputTokens,
          topP: config.topP,
          topK: config.topK
        }
      });

      const content = response.text;
      if (!content) {
        throw new Error('لم يتم الحصول على رد من Gemini');
      }

      return content;
    });
  }
}
