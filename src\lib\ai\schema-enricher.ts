import { GoogleGeminiClient } from './google-gemini-client';
import {
  DatabaseSchema,
  EnrichedDatabaseSchema,
  TableDescription,
  TableInfo,
  ColumnInfo,
  SchemaExtractionProgress
} from '../database/types';

export class SchemaEnricher {
  private geminiClient: GoogleGeminiClient;
  private progressCallbacks: ((progress: SchemaExtractionProgress) => void)[] = [];

  constructor() {
    this.geminiClient = GoogleGeminiClient.getInstance();
  }

  // إضافة مستمع لتحديثات التقدم
  onProgress(callback: (progress: SchemaExtractionProgress) => void): void {
    this.progressCallbacks.push(callback);
  }

  // إزالة مستمع التقدم
  removeProgressListener(callback: (progress: SchemaExtractionProgress) => void): void {
    const index = this.progressCallbacks.indexOf(callback);
    if (index > -1) {
      this.progressCallbacks.splice(index, 1);
    }
  }

  // تحديث التقدم وإشعار المستمعين
  private updateProgress(progress: SchemaExtractionProgress): void {
    this.progressCallbacks.forEach(callback => {
      callback(progress);
    });
  }

  // إثراء Schema بالأوصاف والـ embeddings
  async enrichSchema(schema: DatabaseSchema): Promise<EnrichedDatabaseSchema> {
    const totalTables = schema.tables.length;
    let processedTables = 0;

    // تهيئة التقدم
    const progress: SchemaExtractionProgress = {
      currentStep: 'بدء توليد أوصاف الجداول',
      totalSteps: totalTables + 2, // الجداول + embeddings + حفظ
      currentStepIndex: 0,
      tablesProcessed: 0,
      totalTables,
      isComplete: false,
      logs: ['بدء عملية إثراء Schema بالأوصاف...']
    };

    this.updateProgress(progress);

    try {
      const tableDescriptions: TableDescription[] = [];

      // توليد وصف لكل جدول
      for (const table of schema.tables) {
        progress.currentStep = `تحليل جدول ${table.name}...`;
        progress.currentStepIndex++;
        progress.logs.push(`جاري تحليل جدول ${table.name}...`);
        this.updateProgress(progress);

        try {
          const description = await this.generateTableDescription(table);
          tableDescriptions.push(description);
          
          processedTables++;
          progress.tablesProcessed = processedTables;
          progress.logs.push(`تم تحليل جدول ${table.name} بنجاح`);
          this.updateProgress(progress);

          // تأخير أطول لتجنب rate limiting مع Google Gemini
          await new Promise(resolve => setTimeout(resolve, 3000)); // 3 ثواني بين كل جدول

        } catch (error) {
          console.error(`خطأ في تحليل جدول ${table.name}:`, error);

          let errorMessage = 'خطأ غير معروف';
          if (error instanceof Error) {
            if (error.message.includes('quota') || error.message.includes('429') || error.message.includes('rate')) {
              errorMessage = 'تم تجاوز الحد المسموح لـ Google Gemini API - يرجى الانتظار قليلاً';
            } else if (error.message.includes('404') || error.message.includes('model')) {
              errorMessage = 'النموذج المطلوب غير متاح';
            } else {
              errorMessage = error.message;
            }
          }

          progress.logs.push(`خطأ في تحليل جدول ${table.name}: ${errorMessage}`);
          this.updateProgress(progress);

          // إضافة وصف افتراضي في حالة الخطأ
          tableDescriptions.push({
            tableName: table.name,
            description: `جدول ${table.name} - ${this.generateFallbackDescription(table)}`,
            analyticalValue: 'لم يتم تحليل القيمة التحليلية بسبب خطأ في API',
            sqlExamples: [{
              query: `SELECT * FROM ${table.name} LIMIT 10`,
              explanation: 'استعلام أساسي لعرض البيانات'
            }],
            intelligentAnalysis: 'تم توليد الوصف تلقائياً - يحتاج إلى مراجعة يدوية',
            purpose: this.inferPurposeFromName(table.name),
            domain: this.inferDomainFromColumns(table.columns),
            businessContext: 'تم توليد الوصف تلقائياً بناءً على بنية الجدول',
            keyFields: table.primaryKeys.length > 0 ? table.primaryKeys : table.columns.slice(0, 3).map(c => c.name),
            relatedTables: table.foreignKeys.map(fk => fk.referencedTable),
            limitations: 'وصف تلقائي - قد لا يكون دقيقاً',
            generatedAt: new Date().toISOString()
          });
        }
      }

      // توليد embeddings
      progress.currentStep = 'توليد embeddings للبحث الشعاعي...';
      progress.currentStepIndex++;
      progress.logs.push('جاري توليد embeddings للبحث الشعاعي...');
      this.updateProgress(progress);

      let embeddings: { [tableName: string]: number[] } = {};
      try {
        embeddings = await this.generateEmbeddings(tableDescriptions);
      } catch (error) {
        console.error('خطأ في توليد embeddings:', error);
        progress.logs.push('تم تخطي توليد embeddings بسبب مشكلة في API - سيتم استخدام البحث النصي البديل');
        this.updateProgress(progress);
        // إنشاء embeddings فارغة كبديل
        tableDescriptions.forEach(desc => {
          embeddings[desc.tableName] = [];
        });
      }

      // إنشاء Schema المحسن
      const enrichedSchema: EnrichedDatabaseSchema = {
        ...schema,
        tableDescriptions,
        embeddings
      };

      // اكتمال العملية
      progress.currentStep = 'تم الانتهاء من إثراء Schema';
      progress.currentStepIndex++;
      progress.isComplete = true;
      progress.logs.push('تم إثراء Schema بالأوصاف والـ embeddings بنجاح!');
      this.updateProgress(progress);

      return enrichedSchema;

    } catch (error) {
      progress.error = error instanceof Error ? error.message : 'خطأ غير معروف';
      progress.isComplete = true;
      progress.logs.push(`خطأ في إثراء Schema: ${progress.error}`);
      this.updateProgress(progress);
      throw error;
    }
  }

  // توليد وصف لجدول واحد
  private async generateTableDescription(table: TableInfo): Promise<TableDescription> {
    try {
      // تحضير معلومات الأعمدة
      const columns = table.columns.map(col => ({
        name: col.name,
        type: col.type,
        nullable: col.nullable
      }));

      // تحضير معلومات المفاتيح الخارجية
      const foreignKeys = table.foreignKeys.map(fk => ({
        columnName: fk.columnName,
        referencedTable: fk.referencedTable
      }));

      // توليد الوصف باستخدام Google Gemini
      const aiDescription = await this.geminiClient.generateTableDescription(
        table.name,
        columns,
        foreignKeys
      );

      return {
        tableName: table.name,
        description: aiDescription.description,
        analyticalValue: aiDescription.analyticalValue,
        sqlExamples: aiDescription.sqlExamples,
        intelligentAnalysis: aiDescription.intelligentAnalysis,
        purpose: aiDescription.purpose,
        domain: aiDescription.domain,
        businessContext: aiDescription.businessContext,
        keyFields: aiDescription.keyFields,
        relatedTables: aiDescription.relatedTables,
        limitations: aiDescription.limitations,
        generatedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error(`خطأ في توليد وصف الجدول ${table.name}:`, error);
      throw error;
    }
  }

  // توليد embeddings لجميع أوصاف الجداول
  private async generateEmbeddings(tableDescriptions: TableDescription[]): Promise<{ [tableName: string]: number[] }> {
    const embeddings: { [tableName: string]: number[] } = {};

    for (const description of tableDescriptions) {
      try {
        // دمج جميع معلومات الجدول في نص واحد للـ embedding
        const combinedText = `
          ${description.tableName}
          ${description.description}
          ${description.purpose}
          ${description.domain}
          ${description.businessContext}
          ${description.keyFields.join(' ')}
          ${description.relatedTables.join(' ')}
        `.trim();

        const embedding = await this.geminiClient.generateEmbeddings(combinedText);
        embeddings[description.tableName] = embedding;

        // تأخير أطول لتجنب rate limiting مع Google Gemini
        await new Promise(resolve => setTimeout(resolve, 2000)); // ثانيتان بين كل embedding

      } catch (error) {
        console.error(`خطأ في توليد embedding للجدول ${description.tableName}:`, error);
        // في حالة الخطأ، نضع embedding فارغ
        embeddings[description.tableName] = [];
      }
    }

    return embeddings;
  }

  // البحث عن الجداول الأكثر صلة بسؤال المستخدم
  async findRelevantTables(
    userQuestion: string,
    enrichedSchema: EnrichedDatabaseSchema,
    maxTables: number = 5
  ): Promise<Array<{
    tableName: string;
    similarity: number;
    description: TableDescription;
  }>> {
    try {
      // محاولة استخدام embeddings أولاً
      const hasValidEmbeddings = enrichedSchema.embeddings &&
        Object.values(enrichedSchema.embeddings).some(emb => emb.length > 0);

      if (hasValidEmbeddings) {
        try {
          // توليد embedding لسؤال المستخدم
          const questionEmbedding = await this.geminiClient.generateEmbeddings(userQuestion);

          // حساب التشابه مع كل جدول
          const similarities: Array<{
            tableName: string;
            similarity: number;
            description: TableDescription;
          }> = [];

          for (const description of enrichedSchema.tableDescriptions) {
            const tableEmbedding = enrichedSchema.embeddings?.[description.tableName];

            if (tableEmbedding && tableEmbedding.length > 0) {
              const similarity = this.calculateCosineSimilarity(questionEmbedding, tableEmbedding);
              similarities.push({
                tableName: description.tableName,
                similarity,
                description
              });
            }
          }

          // ترتيب حسب التشابه وإرجاع أفضل النتائج
          return similarities
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, maxTables);
        } catch (embeddingError) {
          console.warn('فشل في استخدام embeddings، التبديل للبحث النصي:', embeddingError);
        }
      }

      // البحث النصي البديل
      return this.findRelevantTablesTextBased(userQuestion, enrichedSchema, maxTables);

    } catch (error) {
      console.error('خطأ في البحث عن الجداول المناسبة:', error);
      // إرجاع جميع الجداول كبديل أخير
      return enrichedSchema.tableDescriptions.slice(0, maxTables).map(desc => ({
        tableName: desc.tableName,
        similarity: 0.5,
        description: desc
      }));
    }
  }

  // البحث النصي البديل
  private findRelevantTablesTextBased(
    userQuestion: string,
    enrichedSchema: EnrichedDatabaseSchema,
    maxTables: number
  ): Array<{
    tableName: string;
    similarity: number;
    description: TableDescription;
  }> {
    const questionLower = userQuestion.toLowerCase();
    const similarities: Array<{
      tableName: string;
      similarity: number;
      description: TableDescription;
    }> = [];

    for (const description of enrichedSchema.tableDescriptions) {
      let score = 0;

      // البحث في اسم الجدول مع أولوية خاصة للجداول المهمة
      if (description.tableName.toLowerCase().includes(questionLower)) {
        score += 10;
      }

      // إعطاء أولوية عالية لجدول items للمنتجات والمبيعات
      if (description.tableName.toLowerCase() === 'items' &&
          (questionLower.includes('منتج') || questionLower.includes('سلعة') || questionLower.includes('بضاعة') ||
           questionLower.includes('مبيعات') || questionLower.includes('مقارنة') || questionLower.includes('بط') ||
           questionLower.includes('ديك') || questionLower.includes('رومي'))) {
        score += 20; // أولوية عالية جداً
      }

      // تقليل أولوية جدول products للمبيعات (لأنه لا يرتبط بـ invoice_details)
      if (description.tableName.toLowerCase() === 'products' &&
          (questionLower.includes('مبيعات') || questionLower.includes('مقارنة') || questionLower.includes('شراء'))) {
        score += 2; // أولوية منخفضة للمبيعات
      } else if (description.tableName.toLowerCase() === 'products') {
        score += 8; // أولوية عادية للاستعلامات الأخرى
      }

      // البحث في الوصف
      const descWords = description.description.toLowerCase().split(' ');
      const questionWords = questionLower.split(' ');

      questionWords.forEach(qWord => {
        if (qWord.length > 2) { // تجاهل الكلمات القصيرة
          descWords.forEach(dWord => {
            if (dWord.includes(qWord) || qWord.includes(dWord)) {
              score += 2;
            }
          });
        }
      });

      // البحث في المجال
      if (description.domain.toLowerCase().includes(questionLower)) {
        score += 5;
      }

      // البحث في الحقول الرئيسية
      description.keyFields.forEach(field => {
        if (field.toLowerCase().includes(questionLower)) {
          score += 3;
        }
      });

      if (score > 0) {
        similarities.push({
          tableName: description.tableName,
          similarity: Math.min(score / 25, 1), // تطبيع النتيجة مع قاعدة أعلى
          description
        });
      }
    }

    // ترتيب حسب النتيجة وإرجاع أفضل النتائج
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, maxTables);
  }

  // حساب التشابه الكوسيني بين vectorين
  private calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      normA += vectorA[i] * vectorA[i];
      normB += vectorB[i] * vectorB[i];
    }

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  // توليد وصف افتراضي بناءً على بنية الجدول
  private generateFallbackDescription(table: TableInfo): string {
    const columnCount = table.columns.length;
    const hasId = table.columns.some(col => col.name.toLowerCase().includes('id'));
    const hasName = table.columns.some(col => col.name.toLowerCase().includes('name'));
    const hasDate = table.columns.some(col => col.type.toLowerCase().includes('date') || col.type.toLowerCase().includes('time'));

    let description = `جدول يحتوي على ${columnCount} عمود`;

    if (hasId) description += '، يتضمن معرف فريد';
    if (hasName) description += '، يحتوي على حقول أسماء';
    if (hasDate) description += '، يتضمن معلومات زمنية';

    return description;
  }

  // استنتاج الغرض من اسم الجدول
  private inferPurposeFromName(tableName: string): string {
    const name = tableName.toLowerCase();

    if (name.includes('user') || name.includes('customer') || name.includes('client')) {
      return 'إدارة المستخدمين والعملاء';
    } else if (name.includes('product') || name.includes('item') || name.includes('goods')) {
      return 'إدارة المنتجات والسلع';
    } else if (name.includes('order') || name.includes('sale') || name.includes('purchase')) {
      return 'إدارة الطلبات والمبيعات';
    } else if (name.includes('payment') || name.includes('transaction') || name.includes('invoice')) {
      return 'إدارة المدفوعات والمعاملات المالية';
    } else if (name.includes('category') || name.includes('type') || name.includes('class')) {
      return 'تصنيف وتنظيم البيانات';
    } else if (name.includes('log') || name.includes('audit') || name.includes('history')) {
      return 'تسجيل وتتبع العمليات';
    } else {
      return 'تخزين وإدارة البيانات';
    }
  }

  // استنتاج المجال من أسماء الأعمدة
  private inferDomainFromColumns(columns: ColumnInfo[]): string {
    const columnNames = columns.map(col => col.name.toLowerCase()).join(' ');

    if (columnNames.includes('price') || columnNames.includes('amount') || columnNames.includes('cost')) {
      return 'التجارة الإلكترونية';
    } else if (columnNames.includes('student') || columnNames.includes('course') || columnNames.includes('grade')) {
      return 'التعليم';
    } else if (columnNames.includes('patient') || columnNames.includes('doctor') || columnNames.includes('medical')) {
      return 'الرعاية الصحية';
    } else if (columnNames.includes('employee') || columnNames.includes('salary') || columnNames.includes('department')) {
      return 'الموارد البشرية';
    } else if (columnNames.includes('account') || columnNames.includes('balance') || columnNames.includes('transaction')) {
      return 'الخدمات المصرفية';
    } else {
      return 'عام';
    }
  }
}
