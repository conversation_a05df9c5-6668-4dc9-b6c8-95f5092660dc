// أنواع البيانات لقواعد البيانات المختلفة
export type DatabaseType = 'mysql' | 'mssql';

// أنواع المصادقة لـ SQL Server
export type AuthenticationType = 'windows' | 'sqlserver';

// معلومات الاتصال بقاعدة البيانات
export interface DatabaseConnection {
  type: DatabaseType;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  useWindowsAuth?: boolean; // للـ SQL Server فقط
  authenticationType?: AuthenticationType; // نوع المصادقة
}

// معلومات العمود
export interface ColumnInfo {
  name: string;
  type: string;
  nullable: boolean;
  defaultValue?: string;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
  maxLength?: number;
  precision?: number;
  scale?: number;
}

// معلومات العلاقة الخارجية
export interface ForeignKeyInfo {
  columnName: string;
  referencedTable: string;
  referencedColumn: string;
  constraintName: string;
}

// معلومات الجدول
export interface TableInfo {
  name: string;
  schema?: string;
  columns: ColumnInfo[];
  foreignKeys: ForeignKeyInfo[];
  primaryKeys: string[];
  indexes: IndexInfo[];
  rowCount?: number;
}

// معلومات الفهرس
export interface IndexInfo {
  name: string;
  columns: string[];
  isUnique: boolean;
  isPrimary: boolean;
}

// بنية قاعدة البيانات الكاملة
export interface DatabaseSchema {
  databaseName: string;
  databaseType: DatabaseType;
  tables: TableInfo[];
  relationships: RelationshipInfo[];
  extractedAt: string;
  version: string;
}

// معلومات العلاقة بين الجداول
export interface RelationshipInfo {
  fromTable: string;
  fromColumn: string;
  toTable: string;
  toColumn: string;
  relationshipType: 'one-to-one' | 'one-to-many' | 'many-to-many';
  constraintName: string;
}

// وصف الجدول المولد بواسطة AI مع التركيز على القيمة التحليلية
export interface TableDescription {
  tableName: string;
  description: string;
  analyticalValue?: string; // التحليلات والاستخراجات الممكنة
  sqlExamples?: Array<{
    query: string;
    explanation: string;
  }>; // نماذج لاستعلامات SQL مع شرح
  intelligentAnalysis?: string; // تحليل ذكي مقترح
  purpose: string;
  domain: string;
  businessContext: string;
  keyFields: string[];
  relatedTables: string[];
  limitations?: string; // تنبيهات خاصة أو قيود
  generatedAt: string;
}

// Schema مع الأوصاف
export interface EnrichedDatabaseSchema extends DatabaseSchema {
  tableDescriptions: TableDescription[];
  embeddings?: { [tableName: string]: number[] };
}

// حالة عملية استخراج Schema
export interface SchemaExtractionProgress {
  currentStep: string;
  totalSteps: number;
  currentStepIndex: number;
  tablesProcessed: number;
  totalTables: number;
  isComplete: boolean;
  error?: string;
  logs: string[];
}
